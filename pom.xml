<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>de.bsommerfeld</groupId>
    <artifactId>pathetic-bukkit</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>

    <properties>
        <revision>5.3.1-SNAPSHOT</revision>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <modules>
        <module>core</module>
        <module>example</module>
        <module>provider</module>
        <module>provider/paper</module>
        <module>provider/spigot</module>
        <module>provider/spigot/v1_8</module>
        <module>provider/spigot/v1_12</module>
        <module>provider/spigot/v1_15</module>
        <module>provider/spigot/v1_16</module>
        <module>provider/spigot/v1_17</module>
        <module>provider/spigot/v1_18</module>
        <module>provider/spigot/v1_18_R2</module>
        <module>provider/spigot/v1_19_R2</module>
        <module>provider/spigot/v1_19_R3</module>
        <module>provider/spigot/v1_20_R1</module>
        <module>provider/spigot/v1_20_R2</module>
        <module>provider/spigot/v1_20_R3</module>
        <module>provider/spigot/v1_20_R4</module>
        <module>provider/spigot/v1_21_R1</module>
        <module>provider/spigot/v1_21_R2</module>
        <module>provider/spigot/v1_21_R3</module>
        <module>provider/spigot/v1_21_R4</module>
        <module>provider/spigot/v1_21_R5</module>
        <module>provider/resolver</module>
    </modules>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>flatten-maven-plugin</artifactId>
                    <version>1.7.0</version>
                    <configuration>
                        <updatePomFile>true</updatePomFile>
                        <flattenMode>resolveCiFriendliesOnly</flattenMode>
                    </configuration>
                    <executions>
                        <execution>
                            <id>flatten</id>
                            <phase>process-resources</phase>
                            <goals>
                                <goal>flatten</goal>
                            </goals>
                        </execution>
                        <execution>
                            <id>flatten.clean</id>
                            <phase>clean</phase>
                            <goals>
                                <goal>clean</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.14.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-shade-plugin</artifactId>
                    <version>3.6.0</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.14.0</version>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.38</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
