<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>de.b<PERSON>mmerfeld</groupId>
        <artifactId>pathetic-bukkit</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>core</artifactId>
    <version>${revision}</version>

    <properties>
        <pathetic.version>5.3.1-SNAPSHOT</pathetic.version>
    </properties>

    <repositories>
        <repository>
            <id>spigot-repo</id>
            <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
        </repository>
        <repository>
            <id>jitpack.io</id>
            <url>https://jitpack.io</url>
        </repository>
    </repositories>

    <dependencies>
        <dependency>
            <groupId>org.spigotmc</groupId>
            <artifactId>spigot-api</artifactId>
            <version>1.21.7-R0.1-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.38</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>2.0.17</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>de.bsommerfeld</groupId>
            <artifactId>engine</artifactId>
            <version>${pathetic.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>de.bsommerfeld</groupId>
            <artifactId>api</artifactId>
            <version>${pathetic.version}</version>
        </dependency>
        <dependency>
            <groupId>de.bsommerfeld</groupId>
            <artifactId>resolver</artifactId>
            <version>${revision}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>de.bsommerfeld</groupId>
            <artifactId>provider</artifactId>
            <version>${revision}</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>