<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>de.bsommerfeld</groupId>
        <artifactId>pathetic-bukkit</artifactId>
        <version>${revision}</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>resolver</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>2.0.17</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.38</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>de.bsommerfeld</groupId>
            <artifactId>paper</artifactId>
            <version>${revision}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>de.bsommerfeld</groupId>
            <artifactId>spigot</artifactId>
            <version>${revision}</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>
