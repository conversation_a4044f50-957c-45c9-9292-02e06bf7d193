name: Build and Attach pathetic-example jar to release

on:
  release:
    types: [published]

jobs:
  build-and-release:
    name: Build and Attach
    runs-on: windows-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v1

      - name: Set up JDK 21
        uses: actions/setup-java@v1
        with:
          distribution: 'corretto'
          java-package: 'jdk'
          java-version: '21'

      - name: Set up Maven
        uses: stCarolas/setup-maven@v4.4
        with:
          maven-version: 3.8.6

      - name: Cache local Maven repository
        uses: actions/cache@v3
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml', '**/*.target') }}
          restore-keys: |
            ${{ runner.os }}-maven-

      - name: Clean install
        run: mvn clean install -U

      - name: Build Example
        run: |
          mvn -U -V -e -B -ntp clean install compile package -pl example -amd -DskipTests

      - name: Upload Example
        uses: actions/upload-artifact@v4
        with:
          name: example
          path: D:\a\pathetic-bukkit\pathetic-bukkit\example\target\pathetic-example.jar

      - name: Attach pathetic-example jar to release
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ github.event.release.upload_url }}
          asset_path: D:\a\pathetic-bukkit\pathetic-bukkit\example\target\pathetic-example.jar
          asset_name: pathetic-example.jar
          asset_content_type: application/java-archive
